#!/usr/bin/env python3
"""
Momentum Auto-Trader
===================
Loads short-term momentum scan results and executes trades based on high-probability signals.
"""

import os
import sys
import json
import time
import signal
import logging
import argparse
import pandas as pd
from datetime import datetime, time as dt_time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Fyers API
from fyers_apiv3 import fyersModel

################################################################################
# CONFIGURATION
################################################################################

# File paths
CSV_PATH = "/Users/<USER>/Desktop/openadvisor/predictions/short_term_momentum_scan.csv"
STATE_FILE = "state.json"
TRADES_FILE = "trades.csv"
LOG_FILE = "autotrader.log"

# Trading parameters
DEFAULT_CAPITAL = 100000
DEFAULT_MAX_TRADES = 25
DEFAULT_POLL_SECS = 10

# Market hours (IST)
MARKET_OPEN = dt_time(9, 15)
MARKET_CLOSE = dt_time(15, 30)
ENTRY_CUTOFF = dt_time(12, 0)

# High-probability filters
MIN_COMPOSITE_SCORE = 92
MIN_VOLUME_RATIO = 3
MIN_RSI = 45
MAX_RSI = 75
MIN_RESISTANCE_BREAK = -1

# Entry thresholds
PULLBACK_MIN = 0.997  # -0.3% from base
PULLBACK_MAX = 1.000  # at base price
BREAKOUT_MIN = 1.003  # +0.3% from base
BREAKOUT_RESISTANCE_MIN = 0

################################################################################
# GLOBALS
################################################################################

FYERS = None
METRICS = {}  # symbol -> metrics dict
FILLED_SYMBOLS = set()
CASH_REMAINING = 0
RUNNING = True

################################################################################
# LOGGING
################################################################################

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%H:%M:%S",
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)
LOG = logging.getLogger("autotrader")

################################################################################
# SIGNAL HANDLING
################################################################################

def signal_handler(signum, frame):
    global RUNNING
    LOG.info("Received signal %d, shutting down gracefully...", signum)
    RUNNING = False

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

################################################################################
# UTILITY FUNCTIONS
################################################################################

def load_state():
    """Load persistent state from JSON"""
    global FILLED_SYMBOLS, CASH_REMAINING
    if Path(STATE_FILE).exists():
        with open(STATE_FILE, 'r') as f:
            state = json.load(f)
            FILLED_SYMBOLS = set(state.get('filled_symbols', []))
            CASH_REMAINING = state.get('cash_remaining', 0)
            LOG.info("Loaded state: %d filled, ₹%.0f remaining", len(FILLED_SYMBOLS), CASH_REMAINING)

def save_state():
    """Save persistent state to JSON"""
    state = {
        'filled_symbols': list(FILLED_SYMBOLS),
        'cash_remaining': CASH_REMAINING
    }
    with open(STATE_FILE, 'w') as f:
        json.dump(state, f)

def is_market_open():
    """Check if market is currently open"""
    now = datetime.now().time()
    return MARKET_OPEN <= now <= MARKET_CLOSE

def is_entry_time():
    """Check if still within entry time window"""
    now = datetime.now().time()
    return now <= ENTRY_CUTOFF

def load_candidates():
    """Load and filter top 25 candidates from CSV"""
    global METRICS
    
    if not Path(CSV_PATH).exists():
        raise FileNotFoundError(f"Scan file not found: {CSV_PATH}")
    
    df = pd.read_csv(CSV_PATH)
    df = df.sort_values('Composite_Score', ascending=False).head(25)
    
    candidates = []
    for _, row in df.iterrows():
        symbol = row['Symbol']
        # Remove .NS suffix if present and create proper Fyers symbol
        clean_symbol = symbol.replace('.NS', '')
        fyers_symbol = f"NSE:{clean_symbol}-EQ"
        candidates.append(fyers_symbol)
        
        # Store metrics for filtering (use clean symbol as key)
        METRICS[clean_symbol] = {
            'composite_score': row['Composite_Score'],
            'volume_ratio': row['Volume_Ratio'],
            'rsi': row['RSI'],
            'resistance_break': row['Resistance_Break'],
            'current_price': row['Current_Price']
        }
    
    LOG.info("Loaded %d candidates from scan", len(candidates))
    return candidates

def setup_fyers(test_mode=False):
    """Initialize Fyers API using same method as working trader"""
    global FYERS
    
    if test_mode:
        LOG.info("🧪 TEST MODE: Skipping authentication")
        return
    
    # Configuration
    APP_ID = os.getenv("FYERS_APP_ID", "JMHITXL31I-100")
    TOKEN_PATH = "access_token.txt"
    
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH) as fp:
            access_token = fp.read().strip()
            try:
                FYERS = fyersModel.FyersModel(token=access_token, is_async=False, client_id=APP_ID, log_path=log_dir)
            except Exception as e:
                # Fallback to empty log path if logs directory has issues
                LOG.warning(f"Log directory issue: {e}, using empty log path")
                FYERS = fyersModel.FyersModel(token=access_token, is_async=False, client_id=APP_ID, log_path="")
            try:
                FYERS.get_profile()
                LOG.info("✅ Loaded cached token.")
                return
            except Exception:
                LOG.info("🔄 Cached token expired; generating new one…")
    
    # Auto-login if no valid token
    LOG.info("🔐 No valid token found. Run authentication:")
    LOG.info("   python auto_login.py")
    raise RuntimeError("Authentication required. Run: python auto_login.py")

def is_high_probability(symbol):
    """Check if symbol meets high-probability criteria"""
    plain_symbol = symbol.replace("NSE:", "").replace("-EQ", "")
    metrics = METRICS.get(plain_symbol, {})
    
    if not metrics:
        return False
    
    return (
        metrics.get('composite_score', 0) >= MIN_COMPOSITE_SCORE and
        metrics.get('volume_ratio', 0) > MIN_VOLUME_RATIO and
        MIN_RSI <= metrics.get('rsi', 0) <= MAX_RSI and
        metrics.get('resistance_break', -999) >= MIN_RESISTANCE_BREAK
    )

def get_entry_signal(symbol, ltp):
    """Check for pullback or breakout entry signal"""
    plain_symbol = symbol.replace("NSE:", "").replace("-EQ", "")
    metrics = METRICS.get(plain_symbol, {})
    base_price = metrics.get('current_price', 0)
    
    if base_price <= 0:
        return None
    
    # Pullback entry: -0.3% to 0% from base
    pullback_min = base_price * PULLBACK_MIN
    pullback_max = base_price * PULLBACK_MAX
    
    if pullback_min <= ltp <= pullback_max:
        return "pullback"
    
    # Breakout entry: +0.3% from base AND resistance break >= 0
    breakout_min = base_price * BREAKOUT_MIN
    resistance_break = metrics.get('resistance_break', -999)
    
    if ltp >= breakout_min and resistance_break >= BREAKOUT_RESISTANCE_MIN:
        return "breakout"
    
    return None

def fetch_quotes(symbols, test_mode=False):
    """Fetch current quotes for symbols"""
    if test_mode:
        import random
        quotes = {}
        for symbol in symbols:
            plain_symbol = symbol.replace("NSE:", "").replace("-EQ", "")
            base_price = METRICS.get(plain_symbol, {}).get('current_price', 100)
            # Random price around base ±2%
            ltp = base_price * random.uniform(0.98, 1.02)
            quotes[symbol] = {'lp': ltp}
        return quotes
    
    if not symbols:
        LOG.warning("No symbols provided to fetch_quotes")
        return {}
    
    symbols_str = ",".join(symbols)
    LOG.info("Making API call for symbols: %s", symbols_str[:100] + "..." if len(symbols_str) > 100 else symbols_str)
    
    data = FYERS.quotes({"symbols": symbols_str})
    LOG.info("API response code: %d", data.get("code", -1))
    
    if data["code"] != 200:
        LOG.error("Quote fetch failed with response: %s", data)
        raise RuntimeError(f"Quote fetch failed: {data}")
    
    raw_quotes = data.get("d", [])
    LOG.info("API returned %d quote items", len(raw_quotes))
    
    quotes = {}
    for item in raw_quotes:
        symbol = item.get("n")
        price_data = item.get("v", {})
        ltp = price_data.get("lp")
        
        if symbol and ltp:
            quotes[symbol] = {'lp': ltp}
        else:
            LOG.warning("Invalid quote data for symbol %s: %s", symbol, item)
    
    LOG.info("Successfully parsed %d valid quotes", len(quotes))
    return quotes

def place_order(symbol, qty, price, test_mode=False):
    """Place market buy order"""
    if test_mode:
        LOG.info("TEST MODE: Would place order for %s x%d @ ₹%.2f", symbol, qty, price)
        return {"code": 200, "id": "TEST_ORDER_123"}
    
    body = {
        "symbol": symbol,
        "qty": qty,
        "type": 2,  # Market order
        "side": 1,  # Buy
        "productType": "CNC",
        "limitPrice": 0,
        "stopPrice": 0,
        "disclosedQty": 0,
        "validity": "DAY",
        "offlineOrder": False
    }
    
    response = FYERS.place_order(body)
    return response

def log_trade(symbol, qty, price, order_id, signal_type):
    """Log trade to CSV"""
    with open(TRADES_FILE, 'a') as f:
        timestamp = datetime.now().isoformat()
        f.write(f"{timestamp},{symbol},{qty},{price:.2f},{order_id},{signal_type}\n")

################################################################################
# MAIN TRADING LOOP
################################################################################

def get_user_inputs():
    """Interactive prompts for trading parameters"""
    print("\n" + "="*60)
    print("🚀 MOMENTUM AUTO-TRADER SETUP")
    print("="*60)
    
    # Capital input
    while True:
        try:
            capital_input = input(f"💰 Enter total capital (default ₹{DEFAULT_CAPITAL:,}): ").strip()
            if not capital_input:
                capital = DEFAULT_CAPITAL
                break
            capital = float(capital_input.replace(',', '').replace('₹', ''))
            if capital <= 0:
                print("❌ Capital must be positive. Try again.")
                continue
            break
        except ValueError:
            print("❌ Invalid amount. Enter a number (e.g., 100000 or 1,00,000)")
    
    # Max trades input
    while True:
        try:
            trades_input = input(f"📊 Max number of stocks to buy (default {DEFAULT_MAX_TRADES}): ").strip()
            if not trades_input:
                max_trades = DEFAULT_MAX_TRADES
                break
            max_trades = int(trades_input)
            if max_trades <= 0:
                print("❌ Must be at least 1 stock. Try again.")
                continue
            if max_trades > 50:
                print("❌ Maximum 50 stocks allowed. Try again.")
                continue
            break
        except ValueError:
            print("❌ Invalid number. Enter an integer (e.g., 25)")
    
    # Calculate and show allocation
    capital_per_stock = capital / max_trades
    print(f"\n📈 ALLOCATION: ₹{capital_per_stock:,.0f} per stock")
    
    # Poll interval
    while True:
        try:
            poll_input = input(f"⏱️  Quote polling interval in seconds (default {DEFAULT_POLL_SECS}): ").strip()
            if not poll_input:
                poll_secs = DEFAULT_POLL_SECS
                break
            poll_secs = int(poll_input)
            if poll_secs < 1:
                print("❌ Minimum 1 second. Try again.")
                continue
            if poll_secs > 60:
                print("❌ Maximum 60 seconds recommended. Try again.")
                continue
            break
        except ValueError:
            print("❌ Invalid number. Enter seconds (e.g., 10)")
    
    # Test mode
    while True:
        test_input = input("🧪 Run in TEST mode? (y/N): ").strip().lower()
        if test_input in ['', 'n', 'no']:
            test_mode = False
            break
        elif test_input in ['y', 'yes']:
            test_mode = True
            break
        else:
            print("❌ Enter 'y' for yes or 'n' for no")
    
    # Confirmation
    print(f"\n" + "="*60)
    print("📋 TRADING CONFIGURATION")
    print("="*60)
    print(f"💰 Total Capital: ₹{capital:,.0f}")
    print(f"📊 Max Stocks: {max_trades}")
    print(f"💵 Per Stock: ₹{capital_per_stock:,.0f}")
    print(f"⏱️  Poll Interval: {poll_secs}s")
    print(f"🧪 Test Mode: {'YES' if test_mode else 'NO (REAL TRADING)'}")
    print("="*60)
    
    if not test_mode:
        print("⚠️  WARNING: This will place REAL ORDERS with REAL MONEY!")
    
    while True:
        confirm = input("\n✅ Proceed with these settings? (y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            break
        elif confirm in ['', 'n', 'no']:
            print("❌ Trading cancelled by user")
            sys.exit(0)
        else:
            print("❌ Enter 'y' to proceed or 'n' to cancel")
    
    return capital, max_trades, poll_secs, test_mode

def main():
    global CASH_REMAINING, RUNNING
    
    parser = argparse.ArgumentParser(description="Momentum Auto-Trader")
    parser.add_argument("--capital", type=float, help="Total capital")
    parser.add_argument("--max-trades", type=int, help="Max number of trades")
    parser.add_argument("--poll", type=int, help="Poll interval in seconds")
    parser.add_argument("--test", action="store_true", help="Test mode (no real orders)")
    parser.add_argument("--batch", action="store_true", help="Batch mode (no interactive prompts)")
    
    args = parser.parse_args()
    
    # Use CLI args if provided, otherwise prompt user
    if args.batch or (args.capital and args.max_trades):
        capital = args.capital or DEFAULT_CAPITAL
        max_trades = args.max_trades or DEFAULT_MAX_TRADES
        poll_secs = args.poll or DEFAULT_POLL_SECS
        test_mode = args.test
    else:
        capital, max_trades, poll_secs, test_mode = get_user_inputs()
    
    LOG.info("Starting Momentum Auto-Trader")
    LOG.info("Capital: ₹%.0f, Max trades: %d, Poll: %ds, Test: %s", 
             capital, max_trades, poll_secs, test_mode)
    
    # Initialize
    load_state()
    CASH_REMAINING = CASH_REMAINING or capital
    capital_per_trade = capital / max_trades
    
    candidates = load_candidates()
    setup_fyers(test_mode)
    
    # Filter out already filled symbols
    active_symbols = [s for s in candidates if s not in FILLED_SYMBOLS]
    LOG.info("Active symbols: %d (filled: %d)", len(active_symbols), len(FILLED_SYMBOLS))
    
    # Create trades.csv header if needed
    if not Path(TRADES_FILE).exists():
        with open(TRADES_FILE, 'w') as f:
            f.write("timestamp,symbol,qty,price,order_id,signal_type\n")
    
    # Main loop
    while RUNNING and len(FILLED_SYMBOLS) < max_trades and active_symbols:
        if not is_market_open():
            LOG.info("Market closed, waiting...")
            time.sleep(60)
            continue
        
        if not is_entry_time():
            LOG.info("Entry time window closed (after 11:00), stopping")
            break
        
        try:
            # Debug: Show what symbols we're trying to fetch
            LOG.info("Attempting to fetch quotes for %d symbols: %s", 
                    len(active_symbols), active_symbols[:3] if active_symbols else "None")
            
            # Fetch quotes for active symbols
            quotes = fetch_quotes(active_symbols, test_mode)
            LOG.info("Fetched quotes for %d symbols", len(quotes))
            
            if not quotes and active_symbols:
                LOG.warning("⚠️ No quotes returned despite having %d active symbols", len(active_symbols))
            
            # Count high-probability symbols for debugging
            high_prob_symbols = []
            for symbol in active_symbols:
                if is_high_probability(symbol):
                    high_prob_symbols.append(symbol.replace("NSE:", "").replace("-EQ", ""))
            LOG.info("High-probability symbols: %d/%d -> %s", len(high_prob_symbols), len(active_symbols), high_prob_symbols)
            
            for symbol in active_symbols[:]:  # Copy list to modify during iteration
                if symbol in FILLED_SYMBOLS:
                    continue
                
                # Check high-probability filter
                if not is_high_probability(symbol):
                    continue
                
                quote = quotes.get(symbol)
                if not quote:
                    plain_symbol = symbol.replace("NSE:", "").replace("-EQ", "")
                    LOG.warning("⚠️ No quote data for high-probability symbol: %s", plain_symbol)
                    continue
                
                ltp = quote['lp']
                signal_type = get_entry_signal(symbol, ltp)
                
                # Debug: Show entry signal evaluation for high-probability symbols
                plain_symbol = symbol.replace("NSE:", "").replace("-EQ", "")
                metrics = METRICS.get(plain_symbol, {})
                base_price = metrics.get('current_price', 0)
                if base_price > 0:
                    pullback_min = base_price * 0.997
                    pullback_max = base_price * 1.000
                    breakout_min = base_price * 1.003
                    resistance = metrics.get('resistance_break', -999)
                    
                    LOG.info("📊 %s: LTP=₹%.2f, Base=₹%.2f | Pullback=[₹%.2f-₹%.2f] Breakout=₹%.2f+ (R=%.2f)", 
                            plain_symbol, ltp, base_price, pullback_min, pullback_max, breakout_min, resistance)
                
                if signal_type:
                    # Calculate position size
                    qty = max(1, int(capital_per_trade / ltp))
                    order_value = qty * ltp
                    
                    if order_value > CASH_REMAINING:
                        LOG.info("Insufficient cash for %s (need ₹%.0f, have ₹%.0f)", 
                                symbol, order_value, CASH_REMAINING)
                        continue
                    
                    LOG.info("🎯 ENTRY SIGNAL (%s): %s @ ₹%.2f (qty: %d)", 
                            signal_type.upper(), symbol, ltp, qty)
                    
                    # Place order
                    response = place_order(symbol, qty, ltp, test_mode)
                    
                    if response.get("code") in [200, 1101]:
                        order_id = response.get("id", "N/A")
                        LOG.info("✅ Order placed: %s x%d @ ₹%.2f (ID: %s)", 
                                symbol, qty, ltp, order_id)
                        
                        # Update state
                        FILLED_SYMBOLS.add(symbol)
                        CASH_REMAINING -= order_value
                        active_symbols.remove(symbol)
                        
                        # Log trade
                        log_trade(symbol, qty, ltp, order_id, signal_type)
                        save_state()
                        
                        LOG.info("Progress: %d/%d trades, ₹%.0f remaining", 
                                len(FILLED_SYMBOLS), max_trades, CASH_REMAINING)
                    else:
                        LOG.error("❌ Order failed: %s", response)
            
            if len(FILLED_SYMBOLS) >= max_trades:
                LOG.info("✅ Reached maximum trades (%d), stopping", max_trades)
                break
                
        except Exception as e:
            LOG.error("Error in main loop: %s", e)
        
        time.sleep(poll_secs)
    
    LOG.info("Trading session complete. Filled: %d, Cash remaining: ₹%.0f", 
             len(FILLED_SYMBOLS), CASH_REMAINING)
    save_state()

if __name__ == "__main__":
    main()