#!/usr/bin/env python3
"""
Simple Auto Login for Fyers - Opens browser, saves token automatically
"""

import os
import json
import time
import webbrowser
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from fyers_apiv3 import fyersModel

# Fyers Credentials
APP_ID = "JMHITXL31I-100"
APP_SECRET = "58XM4NGA9U"
REDIRECT_URI = "https://127.0.0.1"
TOKEN_FILE = "access_token.txt"

def check_saved_token():
    """Check if we have a valid saved token"""
    if os.path.exists(TOKEN_FILE):
        try:
            with open(TOKEN_FILE, 'r') as f:
                token = f.read().strip()
            
            # Test token
            fyers = fyersModel.FyersModel(token=token, is_async=False, client_id=APP_ID, log_path="")
            profile = fyers.get_profile()
            
            if profile.get('code') == 200:
                print("✅ Using saved token")
                return token
            else:
                print("🔄 Saved token expired")
        except:
            print("🔄 Token invalid, getting new one")
    
    return None

def auto_login():
    """Automatic login with browser"""
    print("🚀 FYERS AUTO LOGIN")
    print("=" * 30)
    
    # Check saved token first
    token = check_saved_token()
    if token:
        return token
    
    # Create session
    session = fyersModel.SessionModel(
        client_id=APP_ID,
        secret_key=APP_SECRET,
        redirect_uri=REDIRECT_URI,
        response_type="code",
        state="sample",
        grant_type="authorization_code"
    )
    
    # Generate auth URL
    auth_url = session.generate_authcode()
    
    print("🌐 Opening browser for authentication...")
    print(f"🔗 Auth URL: {auth_url}")
    
    # Auto-open browser
    webbrowser.open(auth_url)
    
    print("\n📋 Steps:")
    print("1. Login in the opened browser")
    print("2. Complete 2FA if required")
    print("3. You'll see 'Can't connect to server' - that's normal!")
    print("4. Copy the FULL URL from browser address bar")
    print("5. Paste it below")
    
    print("\n💡 The URL will look like:")
    print("   https://127.0.0.1/?s=ok&code=200&auth_code=XXXXX&state=sample")

    # Get redirect URL
    while True:
        try:
            redirected_url = input("\n🔗 Paste the full redirected URL: ").strip()

            if not redirected_url:
                print("❌ URL cannot be empty")
                continue

            if "auth_code=" not in redirected_url:
                print("❌ Invalid URL - should contain 'auth_code='")
                continue
            
            # Extract auth_code from URL
            parsed_url = urlparse(redirected_url)
            query_params = parse_qs(parsed_url.query)

            if 'auth_code' not in query_params:
                print("❌ auth_code parameter not found in URL")
                continue

            auth_code = query_params['auth_code'][0]
            print(f"🔑 Extracted auth_code: {auth_code[:50]}...")

            # Generate token
            session.set_token(auth_code)
            response = session.generate_token()
            
            if "access_token" in response:
                access_token = response["access_token"]
                
                # Save token
                with open(TOKEN_FILE, 'w') as f:
                    f.write(access_token)
                
                # Save metadata
                metadata = {
                    "access_token": access_token,
                    "created_at": datetime.now().isoformat(),
                    "app_id": APP_ID,
                    "auto_generated": True
                }
                
                with open("access_token.json", 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                print("✅ Login successful!")
                print(f"💾 Token saved to {TOKEN_FILE}")
                print("🔄 Future runs will be automatic (token reuse)")
                
                return access_token
            else:
                print(f"❌ Token generation failed: {response}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            print("🔄 Please try again")

def test_token(token):
    """Test if token works"""
    try:
        fyers = fyersModel.FyersModel(token=token, is_async=False, client_id=APP_ID, log_path="")
        profile = fyers.get_profile()
        
        if profile.get('code') == 200:
            user_data = profile.get('data', {})
            print(f"👤 Logged in as: {user_data.get('name', 'N/A')}")
            print(f"📱 Client ID: {user_data.get('id', 'N/A')}")
            return True
        else:
            print(f"❌ Profile fetch failed: {profile}")
            return False
    except Exception as e:
        print(f"❌ Token test failed: {e}")
        return False

def main():
    """Main function"""
    # Get token
    token = auto_login()
    
    if token:
        # Test token
        if test_token(token):
            print("\n🎉 SUCCESS! Ready for automated trading!")
            print("\n📋 Next steps:")
            print("   python automated_momentum_trader.py --capital 100000 --companies 1")
            return token
        else:
            print("❌ Token validation failed")
    else:
        print("❌ Authentication failed")
    
    return None

if __name__ == "__main__":
    main()