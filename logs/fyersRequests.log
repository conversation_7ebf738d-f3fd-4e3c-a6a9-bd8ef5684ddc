{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:16:32,824+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:16:32,952+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:17:12,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:17:12,595+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:17:27,817+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:17:58,000+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:17:58,127+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:18:13,257+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:18:28,386+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:18:43,513+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:18:58,680+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:36:12,906+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:36:13,066+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:36:28,245+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:45:22,699+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:45:22,820+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:45:37,993+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:45:53,216+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:46:08,335+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:46:23,481+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:46:38,637+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:46:53,798+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:47:08,937+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:47:58,457+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:47:58,585+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:48:13,734+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:48:28,877+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:51:59,350+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:51:59,469+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:52:14,599+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:52:29,717+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:52:44,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:52:59,999+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:53:15,126+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:53:30,274+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:53:45,425+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:54:00,554+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:54:15,705+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:54:30,852+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:54:45,985+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:55:01,120+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:55:16,274+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:55:31,398+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:55:46,547+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:56:01,708+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:56:16,845+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:56:31,989+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:56:47,110+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:57:02,248+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:57:17,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:57:32,516+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:57:47,638+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:58:02,805+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:58:17,948+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 10:58:20,240+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:58:20,395+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:58:35,546+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:58:50,704+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:59:05,858+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:59:21,005+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:59:36,147+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 10:59:51,276+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:05:54,756+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:05:54,889+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:06:10,029+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:06:25,183+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:06:40,333+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:06:55,463+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:07:10,587+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:07:25,714+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:07:40,869+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:07:52,521+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:07:52,640+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[post_call:81] fyersModel","message":{"Status Code":200,"API":"/orders/sync"},"timestamp":"2025-06-30 11:07:52,816+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:08:07,968+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:08:23,109+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:08:38,232+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:08:53,353+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:33:58,065+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:34:50,851+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:34:50,981+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:01,122+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:11,278+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:21,428+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:31,553+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:41,672+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:35:51,807+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:01,951+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:12,113+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:22,253+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:32,400+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:42,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:36:52,724+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:02,863+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:37:10,797+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:10,940+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:13,004+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:21,121+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:23,137+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:31,266+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:33,346+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:41,414+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:43,525+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:51,555+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:37:53,668+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:01,706+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:03,828+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:11,850+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:14,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:21,989+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:24,150+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:32,112+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:34,307+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:42,255+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:44,452+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:52,400+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:38:54,584+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:02,569+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:04,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:14,860+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:25,009+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:35,133+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:39:37,789+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:37,909+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:45,266+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:53,052+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:39:55,403+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:05,579+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:08,269+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:15,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:23,398+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:25,864+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:36,038+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:38,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:46,177+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:53,689+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:40:56,315+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:06,507+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:08,825+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:16,651+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:24,005+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:26,771+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:36,917+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:47,074+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:41:57,213+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:07,345+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:17,480+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:27,626+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:37,746+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:47,899+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:42:58,048+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:08,183+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:18,404+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:28,553+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:38,691+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:48,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:43:59,039+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:09,155+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:19,283+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:29,459+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:39,611+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:49,766+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:44:59,901+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:45:10,076+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:45:20,257+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:45:30,391+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:45:40,547+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:45:50,690+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:00,827+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:10,954+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:21,123+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:31,284+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:41,412+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:46:51,580+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:01,753+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:11,887+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:22,075+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:32,221+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:42,382+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:47:52,541+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:02,692+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:12,850+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:23,008+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:33,163+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:48:34,781+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:34,925+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:43,408+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:48:53,533+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:49:03,680+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:49:13,815+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:49:23,988+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:49:34,130+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:49:44,326+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:50:08,417+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:08,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:18,711+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:28,857+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:38,997+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:49,155+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:50:59,296+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:51:09,448+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:51:19,657+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:51:29,803+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:51:39,929+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:51:50,069+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:00,213+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:10,372+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:20,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:30,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:40,898+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:52:51,042+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:01,185+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:11,363+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:21,535+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:31,674+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:41,828+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:53:51,968+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:02,131+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:12,302+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:22,485+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:32,620+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:42,754+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:54:52,884+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:03,043+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:13,176+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:23,310+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:33,476+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:43,645+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:55:53,788+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:03,933+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:14,076+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:24,207+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:34,381+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 11:56:43,413+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:43,559+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:44,524+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:56:54,659+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:57:04,799+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[post_call:81] fyersModel","message":{"Status Code":200,"API":"/orders/sync"},"timestamp":"2025-06-30 11:57:04,940+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 11:57:13,691+0530","service":"FyersAPIRequest"}
