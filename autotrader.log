09:51:29 INFO: 🧪 TEST MODE: Skipping authentication
09:51:29 INFO: Starting Momentum Auto‑Trader – capital ₹100000
09:51:29 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:51:44 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:51:59 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:52:14 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:52:29 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:52:44 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:52:59 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
09:53:14 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:05:44 INFO: 🔐 No valid token found. Run authentication:
10:05:44 INFO:    python auto_login.py
10:16:32 INFO: ✅ Loaded cached token.
10:16:32 INFO: Starting Momentum Auto‑Trader – capital ₹10000
10:17:12 INFO: ✅ Loaded cached token.
10:17:12 INFO: Starting Momentum Auto‑Trader – capital ₹10000
10:17:12 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56.8, 'bid': 55.12, 'chp': -2.37, 'ch': -1.34, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55.12, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 1.68, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21345, 'atp': 55.51}, 's': 'ok'}
10:17:12 WARNING: ⚠️ No price data found for NSE:ARENTERP-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:RAYMONDLSL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:HSCL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:GODFRYPHLP-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SANSTAR-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:BANG-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:THOMASCOTT-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:HPIL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SMLISUZU-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:HUBTOWN-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:AVROIND-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:HBSL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:JKLAKSHMI-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SETCO-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SABTNL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:RESPONIND-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:ORBTEXP-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:INDRAMEDCO-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:PFOCUS-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:AKZOINDIA-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:POCL-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SANGAMIND-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:SAURASHCEM-EQ. Available fields: ['n', 'v', 's']
10:17:12 WARNING: ⚠️ No price data found for NSE:LLOYDSENGG-EQ. Available fields: ['n', 'v', 's']
10:17:27 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56.1, 'bid': 55.35, 'chp': -0.81, 'ch': -0.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 56, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.75, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21346, 'atp': 55.51}, 's': 'ok'}
10:17:27 WARNING: ⚠️ No price data found for NSE:ARENTERP-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:RAYMONDLSL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:HSCL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:GODFRYPHLP-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SANSTAR-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:BANG-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:THOMASCOTT-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:HPIL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SMLISUZU-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:HUBTOWN-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:AVROIND-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:HBSL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:JKLAKSHMI-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SETCO-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SABTNL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:RESPONIND-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:ORBTEXP-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:INDRAMEDCO-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:PFOCUS-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:AKZOINDIA-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:POCL-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SANGAMIND-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:SAURASHCEM-EQ. Available fields: ['n', 'v', 's']
10:17:27 WARNING: ⚠️ No price data found for NSE:LLOYDSENGG-EQ. Available fields: ['n', 'v', 's']
10:17:35 INFO: Signal 2 received – exiting gracefully…
10:17:58 INFO: ✅ Loaded cached token.
10:17:58 INFO: Starting Momentum Auto‑Trader – capital ₹10000
10:17:58 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56, 'bid': 55.5, 'chp': -0.81, 'ch': -0.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 56, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.5, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21625, 'atp': 55.52}, 's': 'ok'}
10:17:58 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:18:13 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56, 'bid': 55.5, 'chp': -0.81, 'ch': -0.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 56, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.5, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21625, 'atp': 55.52}, 's': 'ok'}
10:18:13 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:18:28 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56, 'bid': 55.5, 'chp': -0.81, 'ch': -0.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 56, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.5, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21625, 'atp': 55.52}, 's': 'ok'}
10:18:28 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:18:43 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56, 'bid': 55.6, 'chp': -0.81, 'ch': -0.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 56, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.4, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 21625, 'atp': 55.52}, 's': 'ok'}
10:18:43 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:18:58 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 56.6, 'bid': 55.36, 'chp': -1.97, 'ch': -1.11, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55.35, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 1.24, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 22625, 'atp': 55.52}, 's': 'ok'}
10:18:58 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:19:02 INFO: Signal 2 received – exiting gracefully…
10:36:12 INFO: ✅ Loaded cached token.
10:36:12 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:36:13 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 55.75, 'bid': 55.02, 'chp': -1.2, 'ch': -0.68, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55.78, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.73, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24251, 'atp': 55.53}, 's': 'ok'}
10:36:13 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:36:28 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 55.75, 'bid': 55.02, 'chp': -1.2, 'ch': -0.68, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55.78, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.73, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24251, 'atp': 55.53}, 's': 'ok'}
10:36:28 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:36:30 INFO: Signal 2 received – exiting gracefully…
10:43:18 INFO: 🧪 TEST MODE: Skipping authentication
10:43:18 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:43:18 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:43:33 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:43:48 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:44:03 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:44:18 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:44:33 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:44:48 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:45:03 INFO: 🧪 TEST MODE: Generated mock quotes for 25 symbols
10:45:22 INFO: ✅ Loaded cached token.
10:45:22 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:45:22 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.87, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.07, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:45:22 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:45:37 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.87, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.07, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:45:37 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:45:53 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.87, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.07, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:45:53 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:46:08 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.87, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.07, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:46:08 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:46:23 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.87, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.07, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:46:23 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:46:38 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:46:38 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:46:53 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:46:53 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:47:08 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:47:08 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:47:58 INFO: ✅ Loaded cached token.
10:47:58 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:47:58 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:47:58 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:48:13 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.59, 'ch': -1.46, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 55, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24498, 'atp': 55.52}, 's': 'ok'}
10:48:13 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:48:28 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:48:28 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:48:36 INFO: Signal 2 received – exiting gracefully…
10:51:59 INFO: ✅ Loaded cached token.
10:51:59 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:51:59 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:51:59 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:52:14 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:52:14 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:52:29 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:52:29 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:52:44 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:52:44 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:53:00 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:53:00 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:53:15 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:53:15 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:53:30 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:53:30 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:53:45 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:53:45 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:54:00 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:54:00 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:54:15 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:54:15 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:54:30 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:54:30 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:54:45 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:54:45 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:55:01 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:55:01 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:55:16 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:55:16 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:55:31 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:55:31 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:55:46 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.8, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.19, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24508, 'atp': 55.52}, 's': 'ok'}
10:55:46 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:56:01 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.94, 'ch': -1.66, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.8, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24509, 'atp': 55.52}, 's': 'ok'}
10:56:01 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:56:16 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:56:16 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:56:31 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:56:31 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:56:47 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:56:47 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:57:02 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:57:02 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:57:17 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:57:17 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:57:32 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:57:32 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:57:47 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:57:47 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:58:02 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:58:02 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:58:17 INFO: 📊 Sample quote data structure: {'n': 'NSE:ARENTERP-EQ', 'v': {'ask': 54.99, 'bid': 54.6, 'chp': -2.6, 'ch': -1.47, 'description': 'NSE:ARENTERP-EQ', 'exchange': 'NSE', 'fyToken': '1010000000181', 'high_price': 58.18, 'low_price': 53.15, 'lp': 54.99, 'open_price': 57.9, 'original_name': 'NSE:ARENTERP-EQ', 'prev_close_price': 56.46, 'short_name': 'ARENTERP-EQ', 'spread': 0.39, 'symbol': 'NSE:ARENTERP-EQ', 'tt': '1751241600', 'volume': 24525, 'atp': 55.52}, 's': 'ok'}
10:58:17 WARNING: ⚠️ No price data found for NSE:NACLIND-EQ. Available fields: ['n', 'v', 's'], price_data fields: ['n', 'errmsg', 'code', 's']
10:58:19 INFO: Signal 2 received – exiting gracefully…
10:58:20 INFO: ✅ Loaded cached token.
10:58:20 INFO: Starting Momentum Auto‑Trader – capital ₹100000
10:58:20 INFO: 📊 Fetching quotes for 25 active symbols (skipping 0 purchased, 0 invalid)
10:58:20 WARNING: ⚠️ No price data for NSE:NACLIND-EQ - marked as invalid. Available fields: ['n', 'v', 's']
10:58:35 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:58:50 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:59:05 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:59:20 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:59:36 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:59:51 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
10:59:54 INFO: Signal 2 received – exiting gracefully…
11:05:54 INFO: ✅ Loaded cached token.
11:05:54 INFO: Starting Momentum Auto‑Trader – capital ₹100000
11:05:54 INFO: 📊 Fetching quotes for 25 active symbols (skipping 0 purchased, 0 invalid)
11:05:54 WARNING: ⚠️ Invalid quote data for NSE:NACLIND-EQ - marked as invalid
11:06:09 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:06:25 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:06:40 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:06:55 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:07:10 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:07:25 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:07:40 INFO: 📊 Fetching quotes for 24 active symbols (skipping 0 purchased, 1 invalid)
11:07:50 INFO: Signal 2 received – exiting gracefully…
11:07:52 INFO: ✅ Loaded cached token.
11:07:52 INFO: Starting Momentum Auto‑Trader – capital ₹100000
11:07:52 INFO: 📊 Fetching quotes for 25 active symbols (skipping 0 purchased, 0 invalid)
11:07:52 INFO: ✅ Successfully fetched quotes for 25 symbols
11:07:52 INFO: Sample symbols with quotes: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:07:52 INFO: ✅ NSE:ARENTERP-EQ: ENTRY CONDITIONS MET - Price ₹54.60 in zone ₹54.33-₹54.87
11:07:52 INFO: 🎯 ENTRY SIGNAL: NSE:ARENTERP-EQ @ ₹54.60 (qty: 1831, value: ₹99972.60)
11:07:52 INFO: 📈 Placing REAL ORDER:
11:07:52 INFO:    Symbol: NSE:ARENTERP-EQ
11:07:52 INFO:    Quantity: 1831
11:07:52 INFO:    Entry: 54.60
11:07:52 INFO:    Order Value: 99972.60
11:07:52 ERROR: ❌ Order failed: {'code': 1101, 'message': 'Successfully placed order', 's': 'ok', 'id': '25063000154232'}
11:07:52 INFO: ✅ NSE:ARENTERP-EQ purchased | Cash left: ₹-23 | Progress: 1/1 companies
11:07:52 INFO: Maximum companies limit (1) reached
11:08:07 INFO: 📊 Fetching quotes for 24 active symbols (skipping 1 purchased, 0 invalid)
11:08:07 INFO: ✅ Successfully fetched quotes for 24 symbols
11:08:07 INFO: Sample symbols with quotes: ['NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ', 'NSE:GODFRYPHLP-EQ']
11:08:07 INFO: Maximum companies limit (1) reached
11:08:22 INFO: 📊 Fetching quotes for 24 active symbols (skipping 1 purchased, 0 invalid)
11:08:23 INFO: ✅ Successfully fetched quotes for 24 symbols
11:08:23 INFO: Sample symbols with quotes: ['NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ', 'NSE:GODFRYPHLP-EQ']
11:08:23 INFO: Maximum companies limit (1) reached
11:08:38 INFO: 📊 Fetching quotes for 24 active symbols (skipping 1 purchased, 0 invalid)
11:08:38 INFO: ✅ Successfully fetched quotes for 24 symbols
11:08:38 INFO: Sample symbols with quotes: ['NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ', 'NSE:GODFRYPHLP-EQ']
11:08:38 INFO: Maximum companies limit (1) reached
11:08:53 INFO: 📊 Fetching quotes for 24 active symbols (skipping 1 purchased, 0 invalid)
11:08:53 INFO: ✅ Successfully fetched quotes for 24 symbols
11:08:53 INFO: Sample symbols with quotes: ['NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ', 'NSE:GODFRYPHLP-EQ']
11:08:53 INFO: Maximum companies limit (1) reached
11:09:00 INFO: Signal 2 received – exiting gracefully…
11:32:21 INFO: Starting Momentum Auto-Trader
11:32:21 INFO: Capital: ₹10000, Max trades: 1, Poll: 10s, Test: False
11:32:21 INFO: Loaded 25 candidates from scan
11:33:57 INFO: Starting Momentum Auto-Trader
11:33:57 INFO: Capital: ₹10000, Max trades: 1, Poll: 10s, Test: False
11:33:57 INFO: Loaded 25 candidates from scan
11:33:58 INFO: ✅ Loaded cached token.
11:33:58 INFO: Active symbols: 25 (filled: 0)
11:33:58 INFO: Entry time window closed (after 11:00), stopping
11:33:58 INFO: Trading session complete. Filled: 0, Cash remaining: ₹10000
11:34:50 INFO: Starting Momentum Auto-Trader
11:34:50 INFO: Capital: ₹10000, Max trades: 1, Poll: 10s, Test: False
11:34:50 INFO: Loaded state: 0 filled, ₹10000 remaining
11:34:50 INFO: Loaded 25 candidates from scan
11:34:50 INFO: ✅ Loaded cached token.
11:34:50 INFO: Active symbols: 25 (filled: 0)
11:34:50 INFO: Fetched quotes for 0 symbols
11:35:01 INFO: Fetched quotes for 0 symbols
11:35:11 INFO: Fetched quotes for 0 symbols
11:35:21 INFO: Fetched quotes for 0 symbols
11:35:31 INFO: Fetched quotes for 0 symbols
11:35:41 INFO: Fetched quotes for 0 symbols
11:35:51 INFO: Fetched quotes for 0 symbols
11:36:01 INFO: Fetched quotes for 0 symbols
11:36:12 INFO: Fetched quotes for 0 symbols
11:36:22 INFO: Fetched quotes for 0 symbols
11:36:32 INFO: Fetched quotes for 0 symbols
11:36:42 INFO: Fetched quotes for 0 symbols
11:36:52 INFO: Fetched quotes for 0 symbols
11:37:02 INFO: Fetched quotes for 0 symbols
11:37:10 INFO: Starting Momentum Auto-Trader
11:37:10 INFO: Capital: ₹10000, Max trades: 1, Poll: 10s, Test: False
11:37:10 INFO: Loaded state: 0 filled, ₹10000 remaining
11:37:10 INFO: Loaded 25 candidates from scan
11:37:10 INFO: ✅ Loaded cached token.
11:37:10 INFO: Active symbols: 25 (filled: 0)
11:37:10 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:37:10 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:37:10 INFO: API response code: 200
11:37:10 INFO: API returned 25 quote items
11:37:10 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:10 INFO: Successfully parsed 0 valid quotes
11:37:10 INFO: Fetched quotes for 0 symbols
11:37:10 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:37:10 INFO: High-probability symbols: 5/25
11:37:13 INFO: Fetched quotes for 0 symbols
11:37:20 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:37:20 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:37:21 INFO: API response code: 200
11:37:21 INFO: API returned 25 quote items
11:37:21 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:21 INFO: Successfully parsed 0 valid quotes
11:37:21 INFO: Fetched quotes for 0 symbols
11:37:21 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:37:21 INFO: High-probability symbols: 5/25
11:37:23 INFO: Fetched quotes for 0 symbols
11:37:31 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:37:31 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:37:31 INFO: API response code: 200
11:37:31 INFO: API returned 25 quote items
11:37:31 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:31 INFO: Successfully parsed 0 valid quotes
11:37:31 INFO: Fetched quotes for 0 symbols
11:37:31 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:37:31 INFO: High-probability symbols: 5/25
11:37:33 INFO: Fetched quotes for 0 symbols
11:37:41 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:37:41 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:37:41 INFO: API response code: 200
11:37:41 INFO: API returned 25 quote items
11:37:41 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:41 INFO: Successfully parsed 0 valid quotes
11:37:41 INFO: Fetched quotes for 0 symbols
11:37:41 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:37:41 INFO: High-probability symbols: 5/25
11:37:43 INFO: Fetched quotes for 0 symbols
11:37:51 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:37:51 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:37:51 INFO: API response code: 200
11:37:51 INFO: API returned 25 quote items
11:37:51 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:37:51 INFO: Successfully parsed 0 valid quotes
11:37:51 INFO: Fetched quotes for 0 symbols
11:37:51 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:37:51 INFO: High-probability symbols: 5/25
11:37:53 INFO: Fetched quotes for 0 symbols
11:38:01 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:01 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:01 INFO: API response code: 200
11:38:01 INFO: API returned 25 quote items
11:38:01 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:01 INFO: Successfully parsed 0 valid quotes
11:38:01 INFO: Fetched quotes for 0 symbols
11:38:01 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:01 INFO: High-probability symbols: 5/25
11:38:03 INFO: Fetched quotes for 0 symbols
11:38:11 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:11 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:11 INFO: API response code: 200
11:38:11 INFO: API returned 25 quote items
11:38:11 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:11 INFO: Successfully parsed 0 valid quotes
11:38:11 INFO: Fetched quotes for 0 symbols
11:38:11 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:11 INFO: High-probability symbols: 5/25
11:38:14 INFO: Fetched quotes for 0 symbols
11:38:21 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:21 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:21 INFO: API response code: 200
11:38:21 INFO: API returned 25 quote items
11:38:21 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:21 INFO: Successfully parsed 0 valid quotes
11:38:21 INFO: Fetched quotes for 0 symbols
11:38:21 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:21 INFO: High-probability symbols: 5/25
11:38:24 INFO: Fetched quotes for 0 symbols
11:38:31 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:31 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:32 INFO: API response code: 200
11:38:32 INFO: API returned 25 quote items
11:38:32 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:32 INFO: Successfully parsed 0 valid quotes
11:38:32 INFO: Fetched quotes for 0 symbols
11:38:32 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:32 INFO: High-probability symbols: 5/25
11:38:34 INFO: Fetched quotes for 0 symbols
11:38:42 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:42 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:42 INFO: API response code: 200
11:38:42 INFO: API returned 25 quote items
11:38:42 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:42 INFO: Successfully parsed 0 valid quotes
11:38:42 INFO: Fetched quotes for 0 symbols
11:38:42 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:42 INFO: High-probability symbols: 5/25
11:38:44 INFO: Fetched quotes for 0 symbols
11:38:52 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:38:52 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:38:52 INFO: API response code: 200
11:38:52 INFO: API returned 25 quote items
11:38:52 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:38:52 INFO: Successfully parsed 0 valid quotes
11:38:52 INFO: Fetched quotes for 0 symbols
11:38:52 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:38:52 INFO: High-probability symbols: 5/25
11:38:54 INFO: Fetched quotes for 0 symbols
11:39:02 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP.NS-EQ', 'NSE:RAYMONDLSL.NS-EQ', 'NSE:HSCL.NS-EQ']
11:39:02 INFO: Making API call for symbols: NSE:ARENTERP.NS-EQ,NSE:RAYMONDLSL.NS-EQ,NSE:HSCL.NS-EQ,NSE:SANSTAR.NS-EQ,NSE:GODFRYPHLP.NS-EQ,NSE:BA...
11:39:02 INFO: API response code: 200
11:39:02 INFO: API returned 25 quote items
11:39:02 WARNING: Invalid quote data for symbol NSE:ARENTERP.NS-EQ: {'n': 'NSE:ARENTERP.NS-EQ', 'v': {'n': 'NSE:ARENTERP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:RAYMONDLSL.NS-EQ: {'n': 'NSE:RAYMONDLSL.NS-EQ', 'v': {'n': 'NSE:RAYMONDLSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:HSCL.NS-EQ: {'n': 'NSE:HSCL.NS-EQ', 'v': {'n': 'NSE:HSCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SANSTAR.NS-EQ: {'n': 'NSE:SANSTAR.NS-EQ', 'v': {'n': 'NSE:SANSTAR.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:GODFRYPHLP.NS-EQ: {'n': 'NSE:GODFRYPHLP.NS-EQ', 'v': {'n': 'NSE:GODFRYPHLP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:BANG.NS-EQ: {'n': 'NSE:BANG.NS-EQ', 'v': {'n': 'NSE:BANG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:THOMASCOTT.NS-EQ: {'n': 'NSE:THOMASCOTT.NS-EQ', 'v': {'n': 'NSE:THOMASCOTT.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:NACLIND.NS-EQ: {'n': 'NSE:NACLIND.NS-EQ', 'v': {'n': 'NSE:NACLIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:HPIL.NS-EQ: {'n': 'NSE:HPIL.NS-EQ', 'v': {'n': 'NSE:HPIL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SMLISUZU.NS-EQ: {'n': 'NSE:SMLISUZU.NS-EQ', 'v': {'n': 'NSE:SMLISUZU.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:HUBTOWN.NS-EQ: {'n': 'NSE:HUBTOWN.NS-EQ', 'v': {'n': 'NSE:HUBTOWN.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:AVROIND.NS-EQ: {'n': 'NSE:AVROIND.NS-EQ', 'v': {'n': 'NSE:AVROIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:HBSL.NS-EQ: {'n': 'NSE:HBSL.NS-EQ', 'v': {'n': 'NSE:HBSL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:JKLAKSHMI.NS-EQ: {'n': 'NSE:JKLAKSHMI.NS-EQ', 'v': {'n': 'NSE:JKLAKSHMI.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SETCO.NS-EQ: {'n': 'NSE:SETCO.NS-EQ', 'v': {'n': 'NSE:SETCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SABTNL.NS-EQ: {'n': 'NSE:SABTNL.NS-EQ', 'v': {'n': 'NSE:SABTNL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:ORBTEXP.NS-EQ: {'n': 'NSE:ORBTEXP.NS-EQ', 'v': {'n': 'NSE:ORBTEXP.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:INDRAMEDCO.NS-EQ: {'n': 'NSE:INDRAMEDCO.NS-EQ', 'v': {'n': 'NSE:INDRAMEDCO.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:RESPONIND.NS-EQ: {'n': 'NSE:RESPONIND.NS-EQ', 'v': {'n': 'NSE:RESPONIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:PFOCUS.NS-EQ: {'n': 'NSE:PFOCUS.NS-EQ', 'v': {'n': 'NSE:PFOCUS.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:AKZOINDIA.NS-EQ: {'n': 'NSE:AKZOINDIA.NS-EQ', 'v': {'n': 'NSE:AKZOINDIA.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:POCL.NS-EQ: {'n': 'NSE:POCL.NS-EQ', 'v': {'n': 'NSE:POCL.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SANGAMIND.NS-EQ: {'n': 'NSE:SANGAMIND.NS-EQ', 'v': {'n': 'NSE:SANGAMIND.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:SAURASHCEM.NS-EQ: {'n': 'NSE:SAURASHCEM.NS-EQ', 'v': {'n': 'NSE:SAURASHCEM.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 WARNING: Invalid quote data for symbol NSE:LLOYDSENGG.NS-EQ: {'n': 'NSE:LLOYDSENGG.NS-EQ', 'v': {'n': 'NSE:LLOYDSENGG.NS-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:02 INFO: Successfully parsed 0 valid quotes
11:39:02 INFO: Fetched quotes for 0 symbols
11:39:02 WARNING: ⚠️ No quotes returned despite having 25 active symbols
11:39:02 INFO: High-probability symbols: 5/25
11:39:04 INFO: Fetched quotes for 0 symbols
11:39:14 INFO: Fetched quotes for 0 symbols
11:39:25 INFO: Fetched quotes for 0 symbols
11:39:35 INFO: Fetched quotes for 0 symbols
11:39:37 INFO: Starting Momentum Auto-Trader
11:39:37 INFO: Capital: ₹10000, Max trades: 1, Poll: 15s, Test: False
11:39:37 INFO: Loaded state: 0 filled, ₹10000 remaining
11:39:37 INFO: Loaded 25 candidates from scan
11:39:37 INFO: ✅ Loaded cached token.
11:39:37 INFO: Active symbols: 25 (filled: 0)
11:39:37 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:39:37 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:39:37 INFO: API response code: 200
11:39:37 INFO: API returned 25 quote items
11:39:37 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:37 INFO: Successfully parsed 24 valid quotes
11:39:37 INFO: Fetched quotes for 24 symbols
11:39:37 INFO: High-probability symbols: 5/25
11:39:45 INFO: Fetched quotes for 0 symbols
11:39:52 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:39:52 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:39:53 INFO: API response code: 200
11:39:53 INFO: API returned 25 quote items
11:39:53 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:39:53 INFO: Successfully parsed 24 valid quotes
11:39:53 INFO: Fetched quotes for 24 symbols
11:39:53 INFO: High-probability symbols: 5/25
11:39:55 INFO: Fetched quotes for 0 symbols
11:40:05 INFO: Fetched quotes for 0 symbols
11:40:08 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:40:08 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:40:08 INFO: API response code: 200
11:40:08 INFO: API returned 25 quote items
11:40:08 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:40:08 INFO: Successfully parsed 24 valid quotes
11:40:08 INFO: Fetched quotes for 24 symbols
11:40:08 INFO: High-probability symbols: 5/25
11:40:15 INFO: Fetched quotes for 0 symbols
11:40:23 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:40:23 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:40:23 INFO: API response code: 200
11:40:23 INFO: API returned 25 quote items
11:40:23 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:40:23 INFO: Successfully parsed 24 valid quotes
11:40:23 INFO: Fetched quotes for 24 symbols
11:40:23 INFO: High-probability symbols: 5/25
11:40:25 INFO: Fetched quotes for 0 symbols
11:40:36 INFO: Fetched quotes for 0 symbols
11:40:38 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:40:38 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:40:38 INFO: API response code: 200
11:40:38 INFO: API returned 25 quote items
11:40:38 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:40:38 INFO: Successfully parsed 24 valid quotes
11:40:38 INFO: Fetched quotes for 24 symbols
11:40:38 INFO: High-probability symbols: 5/25
11:40:46 INFO: Fetched quotes for 0 symbols
11:40:53 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:40:53 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:40:53 INFO: API response code: 200
11:40:53 INFO: API returned 25 quote items
11:40:53 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:40:53 INFO: Successfully parsed 24 valid quotes
11:40:53 INFO: Fetched quotes for 24 symbols
11:40:53 INFO: High-probability symbols: 5/25
11:40:56 INFO: Fetched quotes for 0 symbols
11:41:06 INFO: Fetched quotes for 0 symbols
11:41:08 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:41:08 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:41:08 INFO: API response code: 200
11:41:08 INFO: API returned 25 quote items
11:41:08 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:41:08 INFO: Successfully parsed 24 valid quotes
11:41:08 INFO: Fetched quotes for 24 symbols
11:41:08 INFO: High-probability symbols: 5/25
11:41:16 INFO: Fetched quotes for 0 symbols
11:41:23 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:41:23 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:41:24 INFO: API response code: 200
11:41:24 INFO: API returned 25 quote items
11:41:24 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:41:24 INFO: Successfully parsed 24 valid quotes
11:41:24 INFO: Fetched quotes for 24 symbols
11:41:24 INFO: High-probability symbols: 5/25
11:41:26 INFO: Fetched quotes for 0 symbols
11:41:36 INFO: Fetched quotes for 0 symbols
11:41:47 INFO: Fetched quotes for 0 symbols
11:41:57 INFO: Fetched quotes for 0 symbols
11:42:07 INFO: Fetched quotes for 0 symbols
11:42:17 INFO: Fetched quotes for 0 symbols
11:42:27 INFO: Fetched quotes for 0 symbols
11:42:37 INFO: Fetched quotes for 0 symbols
11:42:47 INFO: Fetched quotes for 0 symbols
11:42:58 INFO: Fetched quotes for 0 symbols
11:43:08 INFO: Fetched quotes for 0 symbols
11:43:18 INFO: Fetched quotes for 0 symbols
11:43:28 INFO: Fetched quotes for 0 symbols
11:43:38 INFO: Fetched quotes for 0 symbols
11:43:48 INFO: Fetched quotes for 0 symbols
11:43:59 INFO: Fetched quotes for 0 symbols
11:44:09 INFO: Fetched quotes for 0 symbols
11:44:19 INFO: Fetched quotes for 0 symbols
11:44:29 INFO: Fetched quotes for 0 symbols
11:44:39 INFO: Fetched quotes for 0 symbols
11:44:49 INFO: Fetched quotes for 0 symbols
11:44:59 INFO: Fetched quotes for 0 symbols
11:45:10 INFO: Fetched quotes for 0 symbols
11:45:20 INFO: Fetched quotes for 0 symbols
11:45:30 INFO: Fetched quotes for 0 symbols
11:45:40 INFO: Fetched quotes for 0 symbols
11:45:50 INFO: Fetched quotes for 0 symbols
11:46:00 INFO: Fetched quotes for 0 symbols
11:46:10 INFO: Fetched quotes for 0 symbols
11:46:21 INFO: Fetched quotes for 0 symbols
11:46:31 INFO: Fetched quotes for 0 symbols
11:46:41 INFO: Fetched quotes for 0 symbols
11:46:51 INFO: Fetched quotes for 0 symbols
11:47:01 INFO: Fetched quotes for 0 symbols
11:47:11 INFO: Fetched quotes for 0 symbols
11:47:22 INFO: Fetched quotes for 0 symbols
11:47:32 INFO: Fetched quotes for 0 symbols
11:47:42 INFO: Fetched quotes for 0 symbols
11:47:52 INFO: Fetched quotes for 0 symbols
11:48:02 INFO: Fetched quotes for 0 symbols
11:48:12 INFO: Fetched quotes for 0 symbols
11:48:23 INFO: Fetched quotes for 0 symbols
11:48:33 INFO: Fetched quotes for 0 symbols
11:48:34 INFO: Starting Momentum Auto-Trader
11:48:34 INFO: Capital: ₹10000, Max trades: 1, Poll: 30s, Test: False
11:48:34 INFO: Loaded state: 0 filled, ₹10000 remaining
11:48:34 INFO: Loaded 25 candidates from scan
11:48:34 INFO: ✅ Loaded cached token.
11:48:34 INFO: Active symbols: 25 (filled: 0)
11:48:34 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:48:34 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:48:34 INFO: API response code: 200
11:48:34 INFO: API returned 25 quote items
11:48:34 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:48:34 INFO: Successfully parsed 24 valid quotes
11:48:34 INFO: Fetched quotes for 24 symbols
11:48:34 INFO: High-probability symbols: 5/25
11:48:34 INFO: 📊 ARENTERP: LTP=₹54.53, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:48:34 INFO: 📊 RAYMONDLSL: LTP=₹1314.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:48:34 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:48:34 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:48:43 INFO: Fetched quotes for 0 symbols
11:48:53 INFO: Fetched quotes for 0 symbols
11:49:03 INFO: Fetched quotes for 0 symbols
11:49:13 INFO: Fetched quotes for 0 symbols
11:49:23 INFO: Fetched quotes for 0 symbols
11:49:34 INFO: Fetched quotes for 0 symbols
11:49:44 INFO: Fetched quotes for 0 symbols
11:49:51 INFO: Received signal 2, shutting down gracefully...
11:49:54 INFO: Trading session complete. Filled: 0, Cash remaining: ₹10000
11:50:08 INFO: Starting Momentum Auto-Trader
11:50:08 INFO: Capital: ₹10000, Max trades: 1, Poll: 10s, Test: False
11:50:08 INFO: Loaded state: 0 filled, ₹10000 remaining
11:50:08 INFO: Loaded 25 candidates from scan
11:50:08 INFO: ✅ Loaded cached token.
11:50:08 INFO: Active symbols: 25 (filled: 0)
11:50:08 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:08 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:08 INFO: API response code: 200
11:50:08 INFO: API returned 25 quote items
11:50:08 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:08 INFO: Successfully parsed 24 valid quotes
11:50:08 INFO: Fetched quotes for 24 symbols
11:50:08 INFO: High-probability symbols: 5/25
11:50:08 INFO: 📊 ARENTERP: LTP=₹55.64, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:08 INFO: 📊 RAYMONDLSL: LTP=₹1312.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:08 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:08 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:50:18 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:18 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:18 INFO: API response code: 200
11:50:18 INFO: API returned 25 quote items
11:50:18 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:18 INFO: Successfully parsed 24 valid quotes
11:50:18 INFO: Fetched quotes for 24 symbols
11:50:18 INFO: High-probability symbols: 5/25
11:50:18 INFO: 📊 ARENTERP: LTP=₹55.60, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:18 INFO: 📊 RAYMONDLSL: LTP=₹1313.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:18 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:18 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:50:28 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:28 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:28 INFO: API response code: 200
11:50:28 INFO: API returned 25 quote items
11:50:28 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:28 INFO: Successfully parsed 24 valid quotes
11:50:28 INFO: Fetched quotes for 24 symbols
11:50:28 INFO: High-probability symbols: 5/25
11:50:28 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:28 INFO: 📊 RAYMONDLSL: LTP=₹1313.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:28 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:28 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:50:38 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:38 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:38 INFO: API response code: 200
11:50:38 INFO: API returned 25 quote items
11:50:38 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:38 INFO: Successfully parsed 24 valid quotes
11:50:38 INFO: Fetched quotes for 24 symbols
11:50:38 INFO: High-probability symbols: 5/25
11:50:38 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:38 INFO: 📊 RAYMONDLSL: LTP=₹1314.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:38 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:38 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:50:49 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:49 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:49 INFO: API response code: 200
11:50:49 INFO: API returned 25 quote items
11:50:49 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:49 INFO: Successfully parsed 24 valid quotes
11:50:49 INFO: Fetched quotes for 24 symbols
11:50:49 INFO: High-probability symbols: 5/25
11:50:49 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:49 INFO: 📊 RAYMONDLSL: LTP=₹1317.40, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:49 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:49 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:50:59 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:50:59 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:50:59 INFO: API response code: 200
11:50:59 INFO: API returned 25 quote items
11:50:59 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:50:59 INFO: Successfully parsed 24 valid quotes
11:50:59 INFO: Fetched quotes for 24 symbols
11:50:59 INFO: High-probability symbols: 5/25
11:50:59 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:50:59 INFO: 📊 RAYMONDLSL: LTP=₹1317.70, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:50:59 INFO: 📊 BANG: LTP=₹62.50, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:50:59 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:51:09 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:51:09 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:51:09 INFO: API response code: 200
11:51:09 INFO: API returned 25 quote items
11:51:09 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:51:09 INFO: Successfully parsed 24 valid quotes
11:51:09 INFO: Fetched quotes for 24 symbols
11:51:09 INFO: High-probability symbols: 5/25
11:51:09 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:51:09 INFO: 📊 RAYMONDLSL: LTP=₹1318.70, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:51:09 INFO: 📊 BANG: LTP=₹62.68, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:51:09 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:51:19 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:51:19 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:51:19 INFO: API response code: 200
11:51:19 INFO: API returned 25 quote items
11:51:19 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:51:19 INFO: Successfully parsed 24 valid quotes
11:51:19 INFO: Fetched quotes for 24 symbols
11:51:19 INFO: High-probability symbols: 5/25
11:51:19 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:51:19 INFO: 📊 RAYMONDLSL: LTP=₹1318.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:51:19 INFO: 📊 BANG: LTP=₹62.54, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:51:19 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:51:29 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:51:29 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:51:29 INFO: API response code: 200
11:51:29 INFO: API returned 25 quote items
11:51:29 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:51:29 INFO: Successfully parsed 24 valid quotes
11:51:29 INFO: Fetched quotes for 24 symbols
11:51:29 INFO: High-probability symbols: 5/25
11:51:29 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:51:29 INFO: 📊 RAYMONDLSL: LTP=₹1319.50, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:51:29 INFO: 📊 BANG: LTP=₹62.54, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:51:29 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:51:39 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:51:39 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:51:39 INFO: API response code: 200
11:51:39 INFO: API returned 25 quote items
11:51:39 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:51:39 INFO: Successfully parsed 24 valid quotes
11:51:39 INFO: Fetched quotes for 24 symbols
11:51:39 INFO: High-probability symbols: 5/25
11:51:39 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:51:39 INFO: 📊 RAYMONDLSL: LTP=₹1318.30, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:51:39 INFO: 📊 BANG: LTP=₹62.54, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:51:39 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:51:49 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:51:49 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:51:50 INFO: API response code: 200
11:51:50 INFO: API returned 25 quote items
11:51:50 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:51:50 INFO: Successfully parsed 24 valid quotes
11:51:50 INFO: Fetched quotes for 24 symbols
11:51:50 INFO: High-probability symbols: 5/25
11:51:50 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:51:50 INFO: 📊 RAYMONDLSL: LTP=₹1314.90, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:51:50 INFO: 📊 BANG: LTP=₹62.54, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:51:50 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:00 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:00 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:00 INFO: API response code: 200
11:52:00 INFO: API returned 25 quote items
11:52:00 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:00 INFO: Successfully parsed 24 valid quotes
11:52:00 INFO: Fetched quotes for 24 symbols
11:52:00 INFO: High-probability symbols: 5/25
11:52:00 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:00 INFO: 📊 RAYMONDLSL: LTP=₹1314.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:00 INFO: 📊 BANG: LTP=₹62.54, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:00 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:10 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:10 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:10 INFO: API response code: 200
11:52:10 INFO: API returned 25 quote items
11:52:10 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:10 INFO: Successfully parsed 24 valid quotes
11:52:10 INFO: Fetched quotes for 24 symbols
11:52:10 INFO: High-probability symbols: 5/25
11:52:10 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:10 INFO: 📊 RAYMONDLSL: LTP=₹1311.80, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:10 INFO: 📊 BANG: LTP=₹62.66, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:10 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:20 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:20 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:20 INFO: API response code: 200
11:52:20 INFO: API returned 25 quote items
11:52:20 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:20 INFO: Successfully parsed 24 valid quotes
11:52:20 INFO: Fetched quotes for 24 symbols
11:52:20 INFO: High-probability symbols: 5/25
11:52:20 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:20 INFO: 📊 RAYMONDLSL: LTP=₹1310.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:20 INFO: 📊 BANG: LTP=₹62.41, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:20 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:30 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:30 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:30 INFO: API response code: 200
11:52:30 INFO: API returned 25 quote items
11:52:30 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:30 INFO: Successfully parsed 24 valid quotes
11:52:30 INFO: Fetched quotes for 24 symbols
11:52:30 INFO: High-probability symbols: 5/25
11:52:30 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:30 INFO: 📊 RAYMONDLSL: LTP=₹1310.40, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:30 INFO: 📊 BANG: LTP=₹62.41, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:30 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:40 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:40 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:40 INFO: API response code: 200
11:52:40 INFO: API returned 25 quote items
11:52:40 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:40 INFO: Successfully parsed 24 valid quotes
11:52:40 INFO: Fetched quotes for 24 symbols
11:52:40 INFO: High-probability symbols: 5/25
11:52:40 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:40 INFO: 📊 RAYMONDLSL: LTP=₹1309.80, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:40 INFO: 📊 BANG: LTP=₹62.41, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:40 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:52:50 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:52:50 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:52:51 INFO: API response code: 200
11:52:51 INFO: API returned 25 quote items
11:52:51 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:52:51 INFO: Successfully parsed 24 valid quotes
11:52:51 INFO: Fetched quotes for 24 symbols
11:52:51 INFO: High-probability symbols: 5/25
11:52:51 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:52:51 INFO: 📊 RAYMONDLSL: LTP=₹1308.60, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:52:51 INFO: 📊 BANG: LTP=₹62.42, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:52:51 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:01 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:01 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:01 INFO: API response code: 200
11:53:01 INFO: API returned 25 quote items
11:53:01 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:01 INFO: Successfully parsed 24 valid quotes
11:53:01 INFO: Fetched quotes for 24 symbols
11:53:01 INFO: High-probability symbols: 5/25
11:53:01 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:01 INFO: 📊 RAYMONDLSL: LTP=₹1307.20, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:01 INFO: 📊 BANG: LTP=₹62.42, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:01 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:11 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:11 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:11 INFO: API response code: 200
11:53:11 INFO: API returned 25 quote items
11:53:11 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:11 INFO: Successfully parsed 24 valid quotes
11:53:11 INFO: Fetched quotes for 24 symbols
11:53:11 INFO: High-probability symbols: 5/25
11:53:11 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:11 INFO: 📊 RAYMONDLSL: LTP=₹1308.80, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:11 INFO: 📊 BANG: LTP=₹62.42, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:11 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:21 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:21 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:21 INFO: API response code: 200
11:53:21 INFO: API returned 25 quote items
11:53:21 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:21 INFO: Successfully parsed 24 valid quotes
11:53:21 INFO: Fetched quotes for 24 symbols
11:53:21 INFO: High-probability symbols: 5/25
11:53:21 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:21 INFO: 📊 RAYMONDLSL: LTP=₹1310.50, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:21 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:21 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:31 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:31 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:31 INFO: API response code: 200
11:53:31 INFO: API returned 25 quote items
11:53:31 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:31 INFO: Successfully parsed 24 valid quotes
11:53:31 INFO: Fetched quotes for 24 symbols
11:53:31 INFO: High-probability symbols: 5/25
11:53:31 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:31 INFO: 📊 RAYMONDLSL: LTP=₹1311.20, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:31 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:31 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:41 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:41 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:41 INFO: API response code: 200
11:53:41 INFO: API returned 25 quote items
11:53:41 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:41 INFO: Successfully parsed 24 valid quotes
11:53:41 INFO: Fetched quotes for 24 symbols
11:53:41 INFO: High-probability symbols: 5/25
11:53:41 INFO: 📊 ARENTERP: LTP=₹55.58, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:41 INFO: 📊 RAYMONDLSL: LTP=₹1312.20, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:41 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:41 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:53:51 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:53:51 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:53:51 INFO: API response code: 200
11:53:51 INFO: API returned 25 quote items
11:53:51 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:53:51 INFO: Successfully parsed 24 valid quotes
11:53:51 INFO: Fetched quotes for 24 symbols
11:53:51 INFO: High-probability symbols: 5/25
11:53:51 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:53:51 INFO: 📊 RAYMONDLSL: LTP=₹1313.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:53:51 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:53:51 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:01 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:01 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:02 INFO: API response code: 200
11:54:02 INFO: API returned 25 quote items
11:54:02 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:02 INFO: Successfully parsed 24 valid quotes
11:54:02 INFO: Fetched quotes for 24 symbols
11:54:02 INFO: High-probability symbols: 5/25
11:54:02 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:02 INFO: 📊 RAYMONDLSL: LTP=₹1313.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:02 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:02 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:12 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:12 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:12 INFO: API response code: 200
11:54:12 INFO: API returned 25 quote items
11:54:12 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:12 INFO: Successfully parsed 24 valid quotes
11:54:12 INFO: Fetched quotes for 24 symbols
11:54:12 INFO: High-probability symbols: 5/25
11:54:12 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:12 INFO: 📊 RAYMONDLSL: LTP=₹1313.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:12 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:12 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:22 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:22 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:22 INFO: API response code: 200
11:54:22 INFO: API returned 25 quote items
11:54:22 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:22 INFO: Successfully parsed 24 valid quotes
11:54:22 INFO: Fetched quotes for 24 symbols
11:54:22 INFO: High-probability symbols: 5/25
11:54:22 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:22 INFO: 📊 RAYMONDLSL: LTP=₹1316.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:22 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:22 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:32 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:32 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:32 INFO: API response code: 200
11:54:32 INFO: API returned 25 quote items
11:54:32 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:32 INFO: Successfully parsed 24 valid quotes
11:54:32 INFO: Fetched quotes for 24 symbols
11:54:32 INFO: High-probability symbols: 5/25
11:54:32 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:32 INFO: 📊 RAYMONDLSL: LTP=₹1316.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:32 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:32 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:42 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:42 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:42 INFO: API response code: 200
11:54:42 INFO: API returned 25 quote items
11:54:42 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:42 INFO: Successfully parsed 24 valid quotes
11:54:42 INFO: Fetched quotes for 24 symbols
11:54:42 INFO: High-probability symbols: 5/25
11:54:42 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:42 INFO: 📊 RAYMONDLSL: LTP=₹1315.90, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:42 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:42 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:54:52 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:54:52 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:54:52 INFO: API response code: 200
11:54:52 INFO: API returned 25 quote items
11:54:52 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:54:52 INFO: Successfully parsed 24 valid quotes
11:54:52 INFO: Fetched quotes for 24 symbols
11:54:52 INFO: High-probability symbols: 5/25
11:54:52 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:54:52 INFO: 📊 RAYMONDLSL: LTP=₹1315.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:54:52 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:54:52 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:02 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:02 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:03 INFO: API response code: 200
11:55:03 INFO: API returned 25 quote items
11:55:03 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:03 INFO: Successfully parsed 24 valid quotes
11:55:03 INFO: Fetched quotes for 24 symbols
11:55:03 INFO: High-probability symbols: 5/25
11:55:03 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:03 INFO: 📊 RAYMONDLSL: LTP=₹1314.90, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:03 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:03 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:13 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:13 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:13 INFO: API response code: 200
11:55:13 INFO: API returned 25 quote items
11:55:13 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:13 INFO: Successfully parsed 24 valid quotes
11:55:13 INFO: Fetched quotes for 24 symbols
11:55:13 INFO: High-probability symbols: 5/25
11:55:13 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:13 INFO: 📊 RAYMONDLSL: LTP=₹1313.90, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:13 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:13 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:23 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:23 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:23 INFO: API response code: 200
11:55:23 INFO: API returned 25 quote items
11:55:23 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:23 INFO: Successfully parsed 24 valid quotes
11:55:23 INFO: Fetched quotes for 24 symbols
11:55:23 INFO: High-probability symbols: 5/25
11:55:23 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:23 INFO: 📊 RAYMONDLSL: LTP=₹1315.50, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:23 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:23 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:33 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:33 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:33 INFO: API response code: 200
11:55:33 INFO: API returned 25 quote items
11:55:33 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:33 INFO: Successfully parsed 24 valid quotes
11:55:33 INFO: Fetched quotes for 24 symbols
11:55:33 INFO: High-probability symbols: 5/25
11:55:33 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:33 INFO: 📊 RAYMONDLSL: LTP=₹1315.30, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:33 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:33 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:43 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:43 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:43 INFO: API response code: 200
11:55:43 INFO: API returned 25 quote items
11:55:43 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:43 INFO: Successfully parsed 24 valid quotes
11:55:43 INFO: Fetched quotes for 24 symbols
11:55:43 INFO: High-probability symbols: 5/25
11:55:43 INFO: 📊 ARENTERP: LTP=₹55.00, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:43 INFO: 📊 RAYMONDLSL: LTP=₹1314.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:43 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:43 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:55:53 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:55:53 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:55:53 INFO: API response code: 200
11:55:53 INFO: API returned 25 quote items
11:55:53 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:55:53 INFO: Successfully parsed 24 valid quotes
11:55:53 INFO: Fetched quotes for 24 symbols
11:55:53 INFO: High-probability symbols: 5/25
11:55:53 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:55:53 INFO: 📊 RAYMONDLSL: LTP=₹1314.50, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:55:53 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:55:53 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:03 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:03 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:03 INFO: API response code: 200
11:56:03 INFO: API returned 25 quote items
11:56:03 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:03 INFO: Successfully parsed 24 valid quotes
11:56:03 INFO: Fetched quotes for 24 symbols
11:56:03 INFO: High-probability symbols: 5/25
11:56:03 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:03 INFO: 📊 RAYMONDLSL: LTP=₹1315.80, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:03 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:03 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:13 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:13 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:14 INFO: API response code: 200
11:56:14 INFO: API returned 25 quote items
11:56:14 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:14 INFO: Successfully parsed 24 valid quotes
11:56:14 INFO: Fetched quotes for 24 symbols
11:56:14 INFO: High-probability symbols: 5/25
11:56:14 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:14 INFO: 📊 RAYMONDLSL: LTP=₹1314.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:14 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:14 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:24 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:24 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:24 INFO: API response code: 200
11:56:24 INFO: API returned 25 quote items
11:56:24 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:24 INFO: Successfully parsed 24 valid quotes
11:56:24 INFO: Fetched quotes for 24 symbols
11:56:24 INFO: High-probability symbols: 5/25
11:56:24 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:24 INFO: 📊 RAYMONDLSL: LTP=₹1311.00, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:24 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:24 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:34 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:34 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:34 INFO: API response code: 200
11:56:34 INFO: API returned 25 quote items
11:56:34 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:34 INFO: Successfully parsed 24 valid quotes
11:56:34 INFO: Fetched quotes for 24 symbols
11:56:34 INFO: High-probability symbols: 5/25
11:56:34 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:34 INFO: 📊 RAYMONDLSL: LTP=₹1311.20, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:34 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:34 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:43 INFO: Starting Momentum Auto-Trader
11:56:43 INFO: Capital: ₹10000, Max trades: 1, Poll: 30s, Test: False
11:56:43 INFO: Loaded state: 0 filled, ₹10000 remaining
11:56:43 INFO: Loaded 25 candidates from scan
11:56:43 INFO: ✅ Loaded cached token.
11:56:43 INFO: Active symbols: 25 (filled: 0)
11:56:43 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:43 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:43 INFO: API response code: 200
11:56:43 INFO: API returned 25 quote items
11:56:43 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:43 INFO: Successfully parsed 24 valid quotes
11:56:43 INFO: Fetched quotes for 24 symbols
11:56:43 INFO: High-probability symbols: 5/25 -> ['ARENTERP', 'RAYMONDLSL', 'BANG', 'THOMASCOTT', 'NACLIND']
11:56:43 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:43 INFO: 📊 RAYMONDLSL: LTP=₹1309.70, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:43 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:43 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:43 WARNING: ⚠️ No quote data for high-probability symbol: NACLIND
11:56:44 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:44 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:44 INFO: API response code: 200
11:56:44 INFO: API returned 25 quote items
11:56:44 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:44 INFO: Successfully parsed 24 valid quotes
11:56:44 INFO: Fetched quotes for 24 symbols
11:56:44 INFO: High-probability symbols: 5/25
11:56:44 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:44 INFO: 📊 RAYMONDLSL: LTP=₹1309.70, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:44 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:44 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:56:54 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:56:54 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:56:54 INFO: API response code: 200
11:56:54 INFO: API returned 25 quote items
11:56:54 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:56:54 INFO: Successfully parsed 24 valid quotes
11:56:54 INFO: Fetched quotes for 24 symbols
11:56:54 INFO: High-probability symbols: 5/25
11:56:54 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:56:54 INFO: 📊 RAYMONDLSL: LTP=₹1309.60, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:56:54 INFO: 📊 BANG: LTP=₹62.39, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:56:54 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:57:04 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:57:04 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:57:04 INFO: API response code: 200
11:57:04 INFO: API returned 25 quote items
11:57:04 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:57:04 INFO: Successfully parsed 24 valid quotes
11:57:04 INFO: Fetched quotes for 24 symbols
11:57:04 INFO: High-probability symbols: 5/25
11:57:04 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:57:04 INFO: 📊 RAYMONDLSL: LTP=₹1310.10, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:57:04 INFO: 📊 BANG: LTP=₹61.32, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:57:04 INFO: 🎯 ENTRY SIGNAL (PULLBACK): NSE:BANG-EQ @ ₹61.32 (qty: 163)
11:57:04 INFO: ✅ Order placed: NSE:BANG-EQ x163 @ ₹61.32 (ID: 25063000184488)
11:57:04 INFO: Progress: 1/1 trades, ₹5 remaining
11:57:04 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:57:04 INFO: ✅ Reached maximum trades (1), stopping
11:57:04 INFO: Trading session complete. Filled: 1, Cash remaining: ₹5
11:57:13 INFO: Attempting to fetch quotes for 25 symbols: ['NSE:ARENTERP-EQ', 'NSE:RAYMONDLSL-EQ', 'NSE:HSCL-EQ']
11:57:13 INFO: Making API call for symbols: NSE:ARENTERP-EQ,NSE:RAYMONDLSL-EQ,NSE:HSCL-EQ,NSE:SANSTAR-EQ,NSE:GODFRYPHLP-EQ,NSE:BANG-EQ,NSE:THOMA...
11:57:13 INFO: API response code: 200
11:57:13 INFO: API returned 25 quote items
11:57:13 WARNING: Invalid quote data for symbol NSE:NACLIND-EQ: {'n': 'NSE:NACLIND-EQ', 'v': {'n': 'NSE:NACLIND-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}
11:57:13 INFO: Successfully parsed 24 valid quotes
11:57:13 INFO: Fetched quotes for 24 symbols
11:57:13 INFO: High-probability symbols: 5/25 -> ['ARENTERP', 'RAYMONDLSL', 'BANG', 'THOMASCOTT', 'NACLIND']
11:57:13 INFO: 📊 ARENTERP: LTP=₹55.05, Base=₹56.46 | Pullback=[₹56.29-₹56.46] Breakout=₹56.63+ (R=0.00)
11:57:13 INFO: 📊 RAYMONDLSL: LTP=₹1315.70, Base=₹1222.30 | Pullback=[₹1218.63-₹1222.30] Breakout=₹1225.97+ (R=-0.99)
11:57:13 INFO: 📊 BANG: LTP=₹62.17, Base=₹61.36 | Pullback=[₹61.18-₹61.36] Breakout=₹61.54+ (R=-0.21)
11:57:13 INFO: 📊 THOMASCOTT: LTP=₹383.15, Base=₹403.30 | Pullback=[₹402.09-₹403.30] Breakout=₹404.51+ (R=0.00)
11:57:13 WARNING: ⚠️ No quote data for high-probability symbol: NACLIND
